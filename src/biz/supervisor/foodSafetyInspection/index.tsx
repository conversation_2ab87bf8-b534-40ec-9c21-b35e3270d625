import { useEffect, useState } from 'react';
import { useRoute } from '@react-navigation/native';
import { useCheckPermissionFn } from '@src/hooks';
import { supervisorPermission } from '@src/permission';
import Empty from '@src/ui/components/Empty';
import Tabs from '@src/ui/components/Tabs';
import { SafeAreaView } from 'react-native';
import { InspectionTab } from './components/InspectionTab';

export enum InspectionTabEnum {
  待处理 = 1,
  全部 = 2,
}

export default function FoodSafetyInspectionIndex() {
  const { params: { activeTab } = {} } = useRoute() as { params: { activeTab: InspectionTabEnum } };
  const [activeTabKey, setActiveTabKey] = useState<InspectionTabEnum>();

  const { check } = useCheckPermissionFn();

  const tabList = [
    {
      label: '待处理',
      key: InspectionTabEnum.待处理,
      permission: check(supervisorPermission.HomePermissionEnum.食安稽核报备待处理任务),
      component: <InspectionTab isWait activeTabKey={activeTabKey} />,
    },
    {
      label: '全部',
      key: InspectionTabEnum.全部,
      permission: check(['$app$supervisor$food$safety$auditing$report$wait$for$deal$all']),
      component: <InspectionTab activeTabKey={activeTabKey} />,
    },
  ]?.filter((m) => m.permission);

  useEffect(() => {
    if (activeTab) {
      setActiveTabKey(() => {
        return tabList?.some((m) => m.key === activeTab) ? activeTab : tabList?.[0]?.key;
      });

      return;
    }

    setActiveTabKey(tabList?.[0]?.key);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!tabList?.length) {
    return <Empty text="暂无权限" />;
  }

  return (
    <SafeAreaView className="flex-1 bg-[#F7F8FA]">
      <Tabs
        textClassName="text-[#5E5E5E] text-sm"
        textActiveClassName="text-sm"
        indicatorWidth={32}
        onChange={(val: any) => {
          setActiveTabKey(val);
        }}
        activeKey={activeTabKey?.toString()}
      >
        {tabList.map((m) => (
          <Tabs.TabPane key={`${m.key}`} tab={m.label} lazyRender>
            {m?.component}
          </Tabs.TabPane>
        ))}
      </Tabs>
    </SafeAreaView>
  );
}
