import { useEffect, useState } from 'react';
import { useNavigation, useRoute } from '@react-navigation/native';
import {
  FoodSafeReportApplyReason,
  getEnableReasonList,
  useMutationFoodSafeReportApply,
} from '@src/http/service/tactics/foodSafetyReport';
import { Button } from '@src/ui/components/Button';
import Fields from '@src/ui/template/form/Fields';
import { FormProvider } from '@src/ui/template/form/provide';
import { cn } from '@src/utils';
import { debounce } from 'lodash';
import { useForm } from 'react-hook-form';
import { SafeAreaView, ScrollView, Text, View } from 'react-native';

const ISeparator = ({ className }: { className?: string }) => (
  <View className={cn('my-3 h-[0.5px] w-full bg-[#E5E6EB]', className)} />
);

export default function ReportApplicationIndex() {
  const form = useForm({ mode: 'onChange' });
  const { params } = useRoute() as any;
  const navigation = useNavigation();

  const [reasonOption, setReasonOption] = useState<{ label: string; value: string }[]>([]);

  const { mutateAsync } = useMutationFoodSafeReportApply();

  // 监听 applyReason 字段的变化
  const applyReason = form.watch('applyReason');

  useEffect(() => {
    if (applyReason) {
      und
      getEnableReasonList(applyReason === FoodSafeReportApplyReason.个人原因 ? 'PERSONALLY' : 'SHOP').then(
        (res: any) => {
          setReasonOption(() => {
            return (
              res?.map(({ reasonName }: any) => ({
                label: reasonName,
                value: reasonName,
              })) || []
            );
          });
        },
      );
    }
  }, [applyReason]);

  const onFinish = debounce(() => {
    form.handleSubmit(async (values) => {
      const { applyImages, ...rest } = values || {};

      await mutateAsync({
        ...params,
        ...rest,
        applyImages: applyImages?.map((m: { origin: { fileId: string } }) => m?.origin?.fileId)?.filter(Boolean),
      });

      navigation.goBack();
    })();
  }, 500);

  return (
    <SafeAreaView className="flex-1 p-3">
      <ScrollView className="flex-1">
        <FormProvider value={{ form }}>
          <View className="w-full rounded-lg bg-white p-3">
            <Fields.Checkbox
              label="异常申请原因"
              name="applyReason"
              checkboxProps={{
                direction: 'horizontal',
                options: [
                  {
                    label: '个人原因',
                    value: FoodSafeReportApplyReason.个人原因,
                  },
                  {
                    label: '门店原因',
                    value: FoodSafeReportApplyReason.门店原因,
                  },
                ],
              }}
              rules={{
                required: {
                  value: true,
                  message: '请选择异常申请原因',
                },
              }}
            />
            <ISeparator />
            <Fields.Selector
              label="具体原因"
              name="applySpecificReason"
              selectorOption={{ title: '具体原因', options: reasonOption, search: true }}
              componentClassName="flex-col gap-y-2"
              placeholder="请选择具体原因"
              cellContainerClassName="py-0"
              rules={{
                required: {
                  value: true,
                  message: '请选择具体原因',
                },
              }}
            />
          </View>
          <View className="mt-3 w-full rounded-lg bg-white p-3">
            <Fields.Input
              label={'辅助材料'}
              name="applyRemark"
              inputOptions={{
                className: 'h-[8rem] bg-[#F7F8FA] px-3',
                maxLength: 140,
                multiline: true,
                textAlignVertical: 'top',
                placeholder: '请输入详细的情况说明，最多140字',
              }}
              rules={{
                required: {
                  value: true,
                  message: '请填写情况说明',
                },
              }}
            />
            <Fields.Photos
              name="applyImages"
              pickerType={['cameraPhoto', 'cropCameraVideo', 'cropPicker']}
              fileBusinessType="PATROL"
              rules={{
                required: {
                  value: true,
                  message: '请上传附件材料',
                },
              }}
            />
          </View>
        </FormProvider>
      </ScrollView>
      <View className="bg-white px-3 py-2">
        <Button onPress={onFinish}>
          <Text className="text-base font-medium text-white">确定</Text>
        </Button>
      </View>
    </SafeAreaView>
  );
}
