import { useCallback, useEffect, useMemo } from 'react';
import { useIsFocused } from '@react-navigation/native';
import { FlashList } from '@shopify/flash-list';
import { useInfiniteFoodSafeReportApproveList } from '@src/http/service/tactics/foodSafetyReport';
import { BatchDataApplyStatus, BatchDataApplyStatusCN } from '@src/http/service/task-center';
import { Empty, Loading } from '@src/ui/components/components';
import Search from '@src/ui/template/task/Components/Search';
import useSearchOptions from '@src/ui/template/task/useSearchOptions';
import { formatDateToUTC } from '@src/utils';
import formatUtc from '@src/utils/formatUtc';
import dayjs from 'dayjs';
import { Dimensions, RefreshControl, Text, View } from 'react-native';
import { InspectionCard } from './InspectionCard';
import { IItemSeparatorComponent } from '../../foodSafetyComplaint/components/IItemSeparatorComponent';

interface InspectionTabProps {
  isWait?: boolean;
  activeTabKey?: any;
}

export const InspectionTab = ({ isWait, activeTabKey }: InspectionTabProps) => {
  const isFocused = useIsFocused();
  const { params, onChange, options } = useSearchOptions<any>({
    defaultParams: {
      applyBeginTime: formatDateToUTC(dayjs().subtract(1, 'month').startOf('day').format()),
      applyEndTime: formatDateToUTC(dayjs().endOf('day').format()),
      applyStatus: isWait ? BatchDataApplyStatus.待审核 : undefined,
    },
  });

  const { data, isLoading, isFetching, hasNextPage, fetchNextPage, refetch } = useInfiniteFoodSafeReportApproveList({
    queryKey: ['foodSafeReportApproveList', params, isFocused, activeTabKey],
    params: {
      ...params,
      pageSize: 10,
      applyBeginTime: formatUtc(params.applyBeginTime),
      applyEndTime: formatUtc(params.applyEndTime),
      applyStatus: params?.applyStatus ? [params?.applyStatus] : undefined,
    },
    enabled: isFocused,
  });

  // 监听 activeTabKey 变化，刷新数据
  useEffect(() => {
    if (activeTabKey && isFocused) {
      refetch();
    }
  }, [activeTabKey, isFocused, refetch]);

  const dataSource = useMemo(() => {
    if (!data?.pages?.length) {
      return [];
    }

    return data.pages.flatMap((item) => item?.data || []) || [];
  }, [data]);

  const onEndReached = useCallback(async () => {
    try {
      if (!isFetching && hasNextPage) {
        await fetchNextPage();
      }
    } catch (error) {
      console.log('error :>> ', error);

      throw new Error('加载更多失败');
    }
  }, [fetchNextPage, hasNextPage, isFetching]);

  const onRefresh = useCallback(async () => {
    try {
      await refetch();
    } catch (error) {
      console.log(`刷新列表失败: ${error}`);

      throw new Error('刷新列表失败');
    }
  }, [refetch]);

  const ListEmptyComponent = useMemo(() => {
    return (
      <View
        className="flex items-center justify-center"
        style={{
          height: Dimensions.get('window').height * 0.75,
        }}
      >
        {isLoading ? <Loading /> : <Empty />}
      </View>
    );
  }, [isLoading]);

  const ListFooterComponent = useMemo(() => {
    if (!dataSource?.length) {
      return null;
    }

    return (
      <View className="mb-2 flex items-center justify-center py-2">
        <Text className="text-display-base font-normal">
          {isFetching ? '加载中～' : !hasNextPage ? '没有更多了～' : ''}
        </Text>
      </View>
    );
  }, [dataSource?.length, hasNextPage, isFetching]);

  return (
    <View className="flex-1 bg-[#F7F8FA]">
      <Search
        value={{
          time: [dayjs(params?.applyBeginTime).toDate(), dayjs(params?.applyEndTime).toDate()],
          groupId: params?.groupId,
          shopIds: params.shopIds?.length ? params.shopIds.join('') : undefined,
          custom: {
            applyStatus: params?.applyStatus,
          },
        }}
        options={options.concat(
          !isWait
            ? [
                {
                  type: 'Button',
                  divider: true,
                  dataIndex: 'custom',
                  itemOptions: [
                    {
                      title: '状态',
                      titleKey: 'applyStatus',
                      items: [
                        { label: '全部', value: undefined },
                        {
                          label: BatchDataApplyStatusCN[BatchDataApplyStatus.待审核],
                          value: BatchDataApplyStatus.待审核,
                        },
                        {
                          label: BatchDataApplyStatusCN[BatchDataApplyStatus.审核通过],
                          value: BatchDataApplyStatus.审核通过,
                        },
                        {
                          label: BatchDataApplyStatusCN[BatchDataApplyStatus.审核拒绝],
                          value: BatchDataApplyStatus.审核拒绝,
                        },
                        {
                          label: BatchDataApplyStatusCN[BatchDataApplyStatus.已取消],
                          value: BatchDataApplyStatus.已取消,
                        },
                      ],
                    },
                  ],
                },
              ]
            : [],
        )}
        onSearchParamsChange={(val) => {
          onChange({
            applyBeginTime: !val?.time[0]
              ? formatDateToUTC(dayjs().startOf('day'))
              : formatDateToUTC(dayjs(val?.time[0]).startOf('day')),
            applyEndTime: !val?.time[1]
              ? formatDateToUTC(dayjs().endOf('day'))
              : formatDateToUTC(dayjs(val?.time[1]).endOf('day')),
            shopIds: !val?.shopIds ? undefined : [val?.shopIds],
            groupId: val?.groupId,
            ...val.custom,
          });
        }}
      />

      <View className="flex-1 p-3">
        <FlashList
          estimatedItemSize={165}
          showsVerticalScrollIndicator={false}
          ItemSeparatorComponent={IItemSeparatorComponent}
          data={dataSource}
          renderItem={({ item }) => <InspectionCard {...item} onRefresh={onRefresh} />}
          refreshControl={<RefreshControl refreshing={isFetching} onRefresh={onRefresh} />}
          onEndReached={onEndReached}
          ListEmptyComponent={ListEmptyComponent}
          ListFooterComponent={ListFooterComponent}
        />
      </View>
    </View>
  );
};
