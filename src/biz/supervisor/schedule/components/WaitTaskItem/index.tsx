import { useCallback, useMemo, useState } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { FlashList } from '@shopify/flash-list';
import TstOm from '@src/components';
import {
  // useInfiniteQueryCommentTaskList,
  useInfiniteQueryPatrolTaskList,
  useQueryWaitTaskList,
} from '@src/http/service/supervisorTask';
import { TodoTaskQueryTypeEnum } from '@src/http/service/tactics/supervisorTask';
import Inspection from '@src/ui/template/task/Card/inspection';
import { ReviewCard } from '@src/ui/template/task/Card/review';
import { DeviceEventEmitter, NativeEventEmitter, RefreshControl, View } from 'react-native';
import useTacticsTodoTask from './hooks/useTacticsTodoTask';
import { FoodSafetyPlanCard } from '../FoodSafetyPlanList/components/FoodSafetyPlanCard';
import { SortEnum } from '../Sort';
import TaskPatrolItem from '../TaskPatrolItem';

interface TProps {
  status: string;
  /** 是否为灰度用户 */
  isGrayUser?: boolean;
  /** 排序类型 */
  orderType?: SortEnum;
  onHandTransfer: (id: number, isNewInspector?: boolean) => void;
}

export const scheduleEventEmitter = DeviceEventEmitter || new NativeEventEmitter();

export function WaitTaskItem({ status, isGrayUser, orderType, onHandTransfer }: TProps) {
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);

  // 是否是巡检点评任务
  const isPatrolType = ['ONLY_PATROL_REPORT_FIRST_REVIEW', 'ONLY_PATROL_REPORT_SECOND_REVIEW'].includes(status);
  // 是否是点评任务
  const isCommentTask = ['ONLY_REVIEW_TASK_REPORT'].includes(status);
  // 是否是食安稽核任务
  const isFoodSafetyTask = ['ONLY_FOOD_SAFETY_NORMAL_AUDIT_PLAN_TASK'].includes(status);

  const { data, isLoading, isRefetching, refetch } = useQueryWaitTaskList({
    queryKey: ['WaitTaskList', status],
    enabled: !isPatrolType && !isGrayUser && !isCommentTask,
    params: {
      queryType: status,
    },
  });

  const {
    data: patrolTaskData,
    isLoading: isPatrolLoading,
    isFetching: isPatrolTaskFetching,
    isRefetching: isPatrolTaskRefetching,
    hasNextPage,
    fetchNextPage,
    refetch: refetchPatrolTask,
  } = useInfiniteQueryPatrolTaskList({
    queryKey: ['WaitPatrolTaskList', status],
    enabled: isPatrolType,
    params: {
      queryType: status,
    },
  });

  const taskList = useMemo(() => {
    if (isPatrolType) {
      return patrolTaskData?.pages?.flatMap((page) => page?.flatMap((v) => v?.taskResponse));
    }

    return data?.data?.data?.flatMap((item) => item?.taskResponse);
  }, [data, isPatrolType, patrolTaskData]);

  const {
    tacticsTodoTaskList,
    refetch: tacticsRefresh,
    onRefresh: onTacticsRefresh,
    isRefetching: isTacticsRefetching,
    onEndReached: onTacticsTodoTaskEndReached,
    isListDataLoading,
  } = useTacticsTodoTask({
    queryType: status as TodoTaskQueryTypeEnum,
    isTacticsSign: !!isGrayUser,
    orderType,
  });

  const onRefresh = useCallback(async () => {
    try {
      setIsRefreshing(true);
      await (isCommentTask ? tacticsRefresh() : isPatrolType ? refetchPatrolTask() : refetch());
      setTimeout(() => {
        setIsRefreshing(false);
      }, 500);
    } catch (error) {
      console.log(`刷新列表失败: ${error}`);

      throw new Error('刷新列表失败');
    }
  }, [isCommentTask, tacticsRefresh, isPatrolType, refetchPatrolTask, refetch]);

  const onEndReached = useCallback(async () => {
    if (!isPatrolType) {
      return;
    }

    try {
      if (!isPatrolTaskFetching && hasNextPage) {
        await fetchNextPage();
      }
    } catch (error) {
      console.log('error :>> ', error);

      throw new Error('加载更多失败');
    }
  }, [fetchNextPage, hasNextPage, isPatrolTaskFetching, isPatrolType]);

  const handleUpdate = useCallback(async () => {
    await (isGrayUser ? onTacticsRefresh() : onRefresh());
    scheduleEventEmitter.emit('REACT_NATIVE_SCHEDULE_TASK_REFRESH', { refresh: true, isGrayUser });
  }, [isGrayUser, onRefresh, onTacticsRefresh]);

  const renderItem = useCallback(
    ({ item, index }: { item: any; index: number }) => {
      if (isCommentTask) {
        return <ReviewCard {...item} onRefresh={onTacticsRefresh} handleUpdate={handleUpdate} />;
      }

      if (isFoodSafetyTask) {
        return (
          <FoodSafetyPlanCard {...item} onRefresh={onTacticsRefresh} handleUpdate={handleUpdate} index={index + 1} />
        );
      }

      if (isGrayUser) {
        return (
          <Inspection
            {...item}
            isShowHeadStatus={false}
            carryOutStartTime={item?.startTime}
            onRefresh={isGrayUser ? onTacticsRefresh : onRefresh}
            handleUpdate={handleUpdate}
            handleTransfer={onHandTransfer}
          />
        );
      }

      return (
        <TaskPatrolItem
          task={item}
          isGrayUser={isGrayUser}
          handleUpdate={handleUpdate}
          handleTransfer={onHandTransfer}
        />
      );
    },
    [handleUpdate, isCommentTask, isFoodSafetyTask, isGrayUser, onHandTransfer, onRefresh, onTacticsRefresh],
  );

  useFocusEffect(
    useCallback(() => {
      isGrayUser ? onTacticsRefresh() : onRefresh();
    }, [isGrayUser, onRefresh, onTacticsRefresh]),
  );

  if (isLoading || isRefetching || isPatrolLoading || isPatrolTaskRefetching || isListDataLoading) {
    return (
      <View className="h-full items-center justify-center">
        <TstOm.Loading />
      </View>
    );
  }

  return (
    <FlashList
      className="flex-1"
      data={isGrayUser ? tacticsTodoTaskList : taskList}
      numColumns={1}
      horizontal={false}
      estimatedItemSize={isFoodSafetyTask ? 370 : 243}
      showsVerticalScrollIndicator={false}
      renderItem={renderItem}
      ListEmptyComponent={<TstOm.Empty image={require('@assets/images/shop-empty.png')} text="暂无任务" />}
      refreshControl={
        <RefreshControl
          refreshing={isRefetching || isRefreshing || isPatrolTaskFetching || isTacticsRefetching}
          onRefresh={async () => {
            await (isGrayUser ? onTacticsRefresh() : onRefresh());
            await handleUpdate();
          }}
          enabled={true}
          progressBackgroundColor="#ffffff"
        />
      }
      onEndReached={() => {
        if (isPatrolType) {
          onEndReached();
        } else if (isGrayUser && status === 'ONLY_REVIEW_TASK_REPORT') {
          // 策略 点评任务
          onTacticsTodoTaskEndReached();
        }
      }}
    />
  );
}
