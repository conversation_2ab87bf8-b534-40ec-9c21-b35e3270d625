import { useCallback } from 'react';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import TstOm from '@src/components';
import { usePermission } from '@src/hooks';
import { useGetApproveTaskCount } from '@src/http/service/home';
import { HomePermissionEnum } from '@src/permission/supervisor';
import NotFoundScreen from '@src/screens/NotFound';
import { useGray } from '@src/store';
import { ScrollView } from 'react-native';
import { CertificatesNotice } from './components/CertificatesNotice';
import { DifferenceItems } from './components/DifferenceItems';
import { DifferenceItemsStrategy } from './components/DifferenceItemsStrategy';
import { DifferencesWaitingThirdConfirm } from './components/DifferencesWaitingThirdConfirm';
import { FoodSafeWork } from './components/FoodSafeWork';
import { Header } from './components/Header';
import { Kong } from './components/Kong';
import { ReviewMessage } from './components/ReviewMessage';
import ShortcutButton from './components/ShortcutButton';
import { TodayTask } from './components/TodayTask';
import { TransferCollectionStrategy } from './components/Transfer-Strategy';
import { UnrectifiedShops } from './components/UnrectifiedShops';
import { WeeklyReport } from './components/WeeklyReport';

const Home = () => {
  const isGrayUser = useGray((state) => state.isGrayUser);
  const { checkPagePermission, checkPermission } = usePermission();
  const { data, refetch } = useGetApproveTaskCount();
  const navigation = useNavigation<TstOm.StackParams.ScreenNavigationProp['navigation']>();

  useFocusEffect(
    useCallback(() => {
      if (!checkPagePermission(['$app$supervisor$home'])) {
        return;
      }

      refetch();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [refetch]),
  );

  if (!checkPagePermission(['$app$supervisor$home'])) {
    return <NotFoundScreen logout={true} />;
  }

  return (
    <ScrollView
      className="h-full bg-[#f7f9fc] px-3"
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}
    >
      <TstOm.FocusStatusBar translucent backgroundColor="#f7f9fc" />
      {/* 金刚区 */}
      <Kong />
      {/* 公告栏 */}
      <Header />
      {/* 证照到期提醒 */}
      <CertificatesNotice />
      {/* 稽核未整改 */}
      <UnrectifiedShops />
      {/* 今日任务 */}
      <TodayTask />
      {/* 诊断周报 */}
      {checkPermission(HomePermissionEnum.诊断周报) && <WeeklyReport />}
      {/* 诊断周报 */}
      {isGrayUser && checkPermission(HomePermissionEnum['诊断周报(新)']) && <WeeklyReport isGrayUser />}
      {/* 人脸审核 */}
      {data && checkPermission(HomePermissionEnum.人脸审核待办) ? (
        <ShortcutButton
          label="人脸审核待办"
          count={data}
          onPress={() => {
            navigation.navigate('FaceApproval');
          }}
        />
      ) : null}
      {/* 食安稽核到店辅导任务 */}
      <FoodSafeWork />
      {/* 任务转办申请 */}
      {isGrayUser && <TransferCollectionStrategy />}
      {checkPermission(HomePermissionEnum['自检&巡检报告点评结果提醒']) && <ReviewMessage />}
      {/* 差异项审核 */}
      {checkPermission(HomePermissionEnum.差异项审核) && <DifferenceItems />}
      {/* 差异项审核（新） */}
      {checkPermission(HomePermissionEnum['差异项审核(新)']) && <DifferenceItemsStrategy />}
      {/* 差异项三方审核 */}
      {checkPermission(HomePermissionEnum['差异项三方审核(新)']) && <DifferencesWaitingThirdConfirm />}
    </ScrollView>
  );
};

export default Home;
