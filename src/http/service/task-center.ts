/* eslint-disable max-lines */
import { AlarmLevelEnum } from '@src/types/service/diagnosticEnum';
import { TSignType } from '@src/types/service/suervisorTask';
import { UndefinedInitialDataOptions, useMutation, useQuery } from '@tanstack/react-query';
import { transfertatusMap } from './transfer-Strategy';
import http from '../client';
import { ClientError } from '../utils';

export interface InspectionRequest {
  /**
   * 开始时间
   */
  endDate?: any;
  /**
   * 组织id
   */
  groupId?: string;
  /**
   * 页码
   */
  pageNo?: number;
  /**
   * 页大小
   */
  pageSize?: number;
  /**
   * 门店id
   */
  shopIds?: string[];
  /**
   * 门店类型
   */
  shopType?: ShopType;
  /**
   * 开始时间
   */
  startDate?: any;
  /**
   * 任务状态
   */
  taskStatus?: TaskStatus;
  /**
   * 任务二级类型
   */
  taskSubType?: TaskSubType;
  /**
   * 任务处理人/巡检人
   */
  taskUserId?: number;
  /**
   * 是否必检
   */
  mustFlag?: boolean;
}

/**
 * 门店类型
 */
export enum ShopType {
  Direct = 'DIRECT',
  Join = 'JOIN',
}
/**
 * 转办状态 'WAITING_CONFIRM' | 'CREATED' | 'APPROVED' | 'REJECTED' | 'CANCELED'
 */
export enum TaskTransferStatus {
  无需转办 = 'NONE',
  待确认 = 'WAITING_CONFIRM',
  转办待审核 = 'CREATED',
  已处理 = 'APPROVED',
  驳回 = 'REJECTED',
  已取消 = 'CANCELED',
  待接受 = 'WAIT_ACCEPT',
}

/**
 * 任务状态
 */
export enum TaskStatus {
  已提交 = 'AUDITING',
  任务转办审核中 = 'TRANSFER_AUDIT',
  已作废 = 'CANCELED',
  已完成 = 'COMPLETED',
  审核逾期 = 'AUDIT_EXPIRED',
  已逾期 = 'EXPIRED',
  逾期进行中 = 'EXPIRED_RUNNING',
  进行中 = 'RUNNING',
  待开始 = 'WAITING_START',
  待确认到店 = 'WAITING_CONFIRM_ARRIVE',
  转办审核中 = 'TRANSFER_AUDIT',
}

/**
 * 任务二级类型
 */
export enum TaskSubType {
  AI = 'AI',
  DailyDisinfection = 'DAILY_DISINFECTION',
  Diagnostic = 'DIAGNOSTIC',
  FoodSafetyArriveShop = 'FOOD_SAFETY_ARRIVE_SHOP',
  FoodSafetyNormal = 'FOOD_SAFETY_NORMAL',
  FoodSafetyVideo = 'FOOD_SAFETY_VIDEO',
  Normal = 'NORMAL',
  Self = 'SELF',
  StructDisinfection = 'STRUCT_DISINFECTION',
  Video = 'VIDEO',
  DIFFERENCE_ITEM_ARRIVE_SHOP = 'DIFFERENCE_ITEM_ARRIVE_SHOP',
}

export enum ShopStatus {
  Close = 'CLOSE',
  Invalid = 'INVALID',
  Offline = 'OFFLINE',
  Open = 'OPEN',
  Preparing = 'PREPARING',
  TO_BE_OPENED = 'TO_BE_OPENED',
}

/**
 * 任务周期类型
 */
export enum TaskCycleType {
  Daily = 'DAILY',
  Monthly = 'MONTHLY',
  Quarterly = 'QUARTERLY',
  Weekly = 'WEEKLY',
}

export interface Items {
  hasLeaved: boolean;
  hasSigned: boolean;
  id: number;
  start: string;
  end: string;
  signType: TSignType;
  taskName: string;
  mustCheck: boolean | null;
  displayStatus: 'NOT_READY' | 'CREATED' | 'PROCESS'; // 待开始|转办审核中|进行中
  taskStatus: 'RUNNING' | 'WAITING_START' | 'COMPLETED' | 'EXPIRED_RUNNING';
  hasTransfer: boolean;
  applicationStatus: transfertatusMap;
  supportChooseTransfer: boolean;
  taskSubType: 'DIAGNOSTIC' | 'NORMAL';
  alarmLevel: AlarmLevelEnum;
  mustCount?: number;
  selectionCompleted?: boolean;
  selectorNumber?: number;
  hasNeedReview?: boolean;
  hasNeedSecondReview?: boolean;
  hikvisionUserName?: string;
  hikvisionUserId?: string;
  hikUserFeishuNo?: string;
  handoverParty?: any;
  receivingParty?: any;
  shopId?: string;
  shopName?: string;
  shopStatus?: string;
  shopAddress?: string;
  foodSafetyNormalRouteDataId?: string;
}

/**
 * TaskResponse
 */
export interface InspectionResponse {
  /**
   * 重点项标签集
   */
  accentedTermTags?: AccentedTermTag[];
  /**
   * 检查项上传文件设置类型
   */
  allowAlbum?: AllowAlbum;
  /**
   * 任务过期时间 - 允许过期截止时间最后期限
   * 允许过期截止时间最后期限
   */
  allowExpiredTime?: string;
  /**
   * 批次id
   */
  batchId?: number;
  /**
   * 任务开始时间
   */
  beginTime?: string;
  /**
   * 门店是否绑定摄像头
   */
  bindCamera?: boolean;
  /**
   * 抄送人名称
   */
  copyRecipientNames?: string[];
  /**
   * 任务抄送人
   */
  copyRecipients?: number[];
  /**
   * 任务创建时间
   */
  createTime?: string;
  /**
   * 任务创建人
   */
  createUser?: number;
  /**
   * 任务创建人名称
   */
  createUserName?: string;
  /**
   * 是否逾期标识
   */
  deadlinedFlag?: boolean;
  /**
   * 任务过期时间
   */
  expiredTime?: string;
  /**
   * 任务完成项个数
   */
  finishWorksheetCount?: number;
  /**
   * 云眸门店id
   */
  hikvisionStoreId?: string;
  id?: number;
  /**
   * 问题图片集带url
   */
  issueResources?: FileDTO[];
  /**
   * 整改建议
   */
  itemRemark?: string;
  /**
   * 是否已学习
   */
  learned?: boolean;
  /**
   * 未任务完成项个数
   */
  noFinishWorksheetCount?: number;
  /**
   * 不合格原因集
   */
  nonconformityReasons?: string[];
  /**
   * 其他不合格原因
   */
  otherReason?: string;
  /**
   * 处理详情
   */
  process?: TaskProcessResponse[];
  /**
   * 整改期限，单位：毫秒
   */
  reformLimit?: number;
  /**
   * 整改是否必须上传图片0否1是
   */
  reformMustUpload?: boolean;
  /**
   * 任务备注
   */
  remark?: string;
  /**
   * 执行角色名称
   */
  roleNames?: string[];
  /**
   * 门店地址
   */
  shopAddress?: string;
  /**
   * 门店类型
   */
  shopType?: 'DIRECT' | 'JOIN';
  /**
   * 门店id
   */
  shopId?: string;
  /**
   * 门店维度
   */
  shopLat?: number;
  /**
   * 门店经纬度
   */
  shopLog?: number;
  /**
   * 门店负责人id
   */
  shopManagerId?: number;
  /**
   * 门店负责人姓名
   */
  shopManagerName?: string;
  /**
   * 门店负责人手机号
   */
  shopManagerPhoneNumber?: string;
  /**
   * 门店名称
   */
  shopName?: string;
  /**
   * 门店状态
   * PREPARING：准备中, OPEN：营业中, OFFLINE：停业中, CLOSE：闭店中, INVALID：无效门店；
   * 门店状态：PREPARING：准备中, OPEN：营业中, OFFLINE：停业中, CLOSE：闭店中, INVALID：无效门店；TO_BE_OPENED:待营业
   */
  shopStatus?: ShopStatus;
  /**
   * 任务状态
   */
  status?: TaskStatus;
  /**
   * 任务完成时间
   */
  submitTime?: string;
  /**
   * 是否是子任务
   */
  subTask?: boolean;
  /**
   * 任务周期类型
   */
  taskCycleType?: TaskCycleType;
  /**
   * 任务日期
   */
  taskDate?: string;
  /**
   * 任务种类
   */
  taskKind?: TaskKind;
  /**
   * 任务名称
   */
  taskName?: string;
  /**
   * 任务执行的角色
   */
  taskRoles?: number[];
  /**
   * 任务子类型
   */
  taskSubType?: TaskSubType;
  /**
   * 任务类型
   */
  taskType?: TaskType;
  /**
   * 任务处理人
   */
  taskUser?: number;
  /**
   * 任务处理人名称
   */
  taskUserName?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 检查表分类名
   */
  worksheetCategoryName?: string;
  /**
   * 任务总检查项个数
   */
  worksheetCount?: number;
  /**
   * 检查项名
   */
  worksheetItemContent?: string;
  /**
   * 自检
   * 任务检查表列表
   */
  worksheetList?: TaskWorksheetDTO[];
  /**
   * 整改
   * 检查表名称
   */
  worksheetName?: string;
  items?: Items[];
  [property: string]: any;
}

export enum AccentedTermTag {
  Key = 'KEY',
  Necessary = 'NECESSARY',
  Penalty = 'PENALTY',
  Positive = 'POSITIVE',
  RedLine = 'RED_LINE',
  Yellow = 'YELLOW',
}

/**
 * 检查项上传文件设置类型
 */
export enum AllowAlbum {
  Allow = 'ALLOW',
  FollowingSystem = 'FOLLOWING_SYSTEM',
  NotAllow = 'NOT_ALLOW',
}

/**
 * FileDTO
 */
export interface FileDTO {
  contentType?: null | string;
  /**
   * 文件类型
   */
  fileType?: FileType;
  id?: null | string;
  name?: null | string;
  /**
   * 快照url
   */
  snapshotUrl?: null | string;
  url?: null | string;
  [property: string]: any;
}

/**
 * 文件类型
 */
export enum FileType {
  Excel = 'EXCEL',
  Img = 'IMG',
  PDF = 'PDF',
  Video = 'VIDEO',
}

/**
 * com.tastien.template.biz.domain.task.dto.TaskProcessResponse
 *
 * TaskProcessResponse
 */
export interface TaskProcessResponse {
  /**
   * 创建时间
   * create_time
   */
  createTime?: null | string;
  /**
   * 提交的图片信息
   */
  imageResources?: FileDTO[] | null;
  /**
   * 操作动作枚举值
   */
  operateAction?: OperateAction;
  /**
   * 操作人ID
   * 操作人
   */
  operatorName?: null | string;
  /**
   * 提交内容或备注
   */
  remark?: null | string;
  /**
   * 任务id
   */
  taskId?: number | null;
  /**
   * 修改时间
   * update_time
   */
  updateTime?: null | string;
}

/**
 * 操作动作枚举值
 */
export enum OperateAction {
  Audited = 'AUDITED',
  Canceled = 'CANCELED',
  Confirmed = 'CONFIRMED',
  Created = 'CREATED',
  FeedBack = 'FEED_BACK',
  Rejected = 'REJECTED',
  Reviews = 'REVIEWS',
  Revocation = 'REVOCATION',
  RevocationReviews = 'REVOCATION_REVIEWS',
  RevocationWorksheet = 'REVOCATION_WORKSHEET',
  Submitted = 'SUBMITTED',
}

/**
 * 任务种类
 */
export enum TaskKind {
  Base = 'BASE',
  Extend = 'EXTEND',
}

/**
 * 任务类型
 */
export enum TaskType {
  Issue = 'ISSUE',
  Patrol = 'PATROL',
  Review = 'REVIEW',
  Self = 'SELF',
}

/**
 * 基础检查表实体
 *
 * TaskWorksheetDTO
 */
export interface TaskWorksheetDTO {
  /**
   * 是否已提交
   */
  isSubmit?: boolean;
  /**
   * 检查表编号
   */
  worksheetId?: number;
  /**
   * 检查表名称
   */
  worksheetName?: string;
}

export type TaskTypeListItem = {
  id: number;
  taskType: string;
};

export const useQueryList = (
  option?: Omit<UndefinedInitialDataOptions<TaskTypeListItem[], ClientError>, 'queryFn' | 'queryKey'>,
) => {
  return useQuery<TaskTypeListItem[], ClientError>({
    queryKey: ['queryList'],
    queryFn: () => http.post('/om-api/common/task/type/queryList'),
    ...option,
  });
};

/**
 * 获取检查表
 */
export const useWorkSheets = (
  option?: Omit<UndefinedInitialDataOptions<{ id: number; name: string }[], ClientError>, 'queryFn' | 'queryKey'>,
) => {
  return useQuery<{ id: number; name: string }[], ClientError>({
    queryKey: ['worksheet'],
    queryFn: () => http.get('/om-api/common/disinfection/company/list/simple'),
    ...option,
  });
};

/**
 * 获取消杀巡检人
 */
export const useDisinfectionCompany = (
  option?: Omit<UndefinedInitialDataOptions<{ id: number; name: string }[], ClientError>, 'queryFn' | 'queryKey'>,
) => {
  return useQuery<{ id: number; name: string }[], ClientError>({
    queryKey: ['company'],
    queryFn: () => http.get('/om-api/common/disinfection/company/list/simple'),
    ...option,
  });
};

/**
 * 获取消杀公司
 */
export const useDisinfectionPeople = (
  option?: Omit<UndefinedInitialDataOptions<{ id: number; name: string }[], ClientError>, 'queryFn' | 'queryKey'>,
) => {
  return useQuery<{ id: number; name: string }[], ClientError>({
    queryKey: ['people'],
    queryFn: () => http.get('/om-api/common/disinfection/company/employee/list/simple'),
    ...option,
  });
};

/**
 * 提交反馈
 */
export const useSubmitRectification = () => {
  return useMutation<any, ClientError, { id: number; remark: string; images: string[] }>({
    mutationFn: (params) => http.post('/tm-api/common/issue/submitRectification', params),
  });
};

/**
 * 提交驳回
 */
export const usePassRectification = () => {
  return useMutation<any, ClientError, { id: number; remark?: string; images?: string[] }>({
    mutationFn: (params) => http.post('/tm-api/common/issue/passRectification', params),
  });
};

/**
 * 整改任务撤回
 * @returns
 */
export const useRectificationWithdraw = () => {
  return useMutation<any, ClientError, { id: number; remark?: string }>({
    mutationFn: (params) => http.post('/tm-api/shop/issue/revocationRectification', params),
  });
};

/**
 * 审核通过
 */
export const useRejectRectification = () => {
  return useMutation<any, ClientError, { id: number; remark?: string; images?: string[] }>({
    mutationFn: (params) => http.post('/tm-api/common/issue/rejectRectification', params),
  });
};

/**
 * 整改审核通过并中止循环任务
 */
export const usePassAndCloseLoopTask = () => {
  return useMutation<any, ClientError, { id: number; remark?: string; images?: string[] }>({
    mutationFn: (params) => http.post('/tm-api/common/issue/passRectificationAndCloseLoopTask', params),
  });
};

/**
 * 中止循环任务
 */
export const useCloseLoopTask = () => {
  return useMutation<any, ClientError, { taskId: number; remark?: string }>({
    mutationFn: (params) => http.post('/tm-api/common/loop/issue/task/close', params),
  });
};

/**
 * 获取稽核报告不合格原因
 */
export const useGetBelowStandard = (
  option?: Omit<UndefinedInitialDataOptions<{ id: number; name: string }[], ClientError>, 'queryFn' | 'queryKey'> & {
    params: {
      id: number;
      taskItemId: number;
    };
  },
) => {
  return useQuery<any, ClientError>({
    queryKey: ['belowStandard', option?.params],
    queryFn: () =>
      http.get('/tm-api/common/report/worksheet/items/ref', {
        params: option?.params,
      }),
    ...option,
  });
};
/**
 * 任务中心-是否超管
 */

export const useIsSuperManger = ({
  ...config
}: Omit<UndefinedInitialDataOptions<{ data: boolean }, ClientError>, 'queryFn'> & {
  params?: {};
}) => {
  return useQuery<{ data: boolean }, ClientError>({
    ...config,
    queryKey: config?.queryKey,
    queryFn: () => http.post('/tm-api/corp/sa/isSaRole', { ...config.params }),
  });
};
