import TstOm from '@src/components';
import ExceptionReport from '@src/http/exceptionReport';
import { Linking, PermissionsAndroid, Platform } from 'react-native';
import { Geolocation, init } from 'react-native-amap-geolocation';
import type { LatLng, MapView } from 'react-native-amap3d';
import Config from 'react-native-config';
import { create } from 'zustand';

type LocationInfoState = {
  // 获取的当前经纬度
  location: LatLng | null;
  // 地图上滑动的经纬度
  mapLocation: LatLng;
  selectedLocation: LatLng | null;
  /** 是否为模糊定位 */
  isObscurePositioning: boolean;
  /** 当前城市citycode */
  citycode: string | null;
  locationInfo: Record<string, any>;
  moveCameraWithLocation: (mapViewRef: React.RefObject<MapView>) => Promise<void>;
  setLocation: (location: LatLng) => void;
  distance: string;
  getDistance: (position: LatLng) => void;
  setLocationInfo: (locationInfo: Record<string, any>) => void;
  setSelectedLocation: (selectedLocation: LatLng | null) => void;
  setMapLocation: (location: LatLng) => void;
  setCityCode: (citycode: string) => void;
};

export const useLocation = create<LocationInfoState>((set) => ({
  locationInfo: {},
  location: null,
  mapLocation: {
    latitude: 39.91095,
    longitude: 116.37296,
  },
  citycode: null,
  selectedLocation: null,
  /** 是否为模糊定位 */
  isObscurePositioning: false,
  distance: '0',
  moveCameraWithLocation: async (mapViewRef: React.RefObject<MapView>) => {
    if (Platform.OS === 'android') {
      await PermissionsAndroid.requestMultiple([
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
      ]);
    }

    init({
      android: Config.APP_TD_AMAP_ANDROID_KEY,
      ios: Config.APP_TD_AMAP_IOS_KEY,
    })
      .then(() => {
        Geolocation.getCurrentPosition(
          ({ coords }) => {
            set({
              isObscurePositioning: false,
              mapLocation: { latitude: coords?.latitude, longitude: coords.longitude },
              location: { latitude: coords?.latitude, longitude: coords?.longitude },
            });
            mapViewRef.current?.moveCamera(
              {
                zoom: 18,
                target: { latitude: coords?.latitude ?? 39.91095, longitude: coords.longitude ?? 116.37296 },
              },
              1000,
            );
          },
          (err) => {
            console.error('获取定位失败', err);

            if ([2, 7].includes(err?.code)) {
              ExceptionReport({
                message: `定位失败: ${err?.message}`,
                bizName: '定位失败:(moveCameraWithLocation)',
              });
            }

            if (err?.message?.includes('糊定位失败')) {
              set({ isObscurePositioning: true });
            }
          },
        );
      })
      .catch((err) => console.error('初始化失败', err));
  },
  getDistance: (position) => {
    init({
      android: Config.APP_TD_AMAP_ANDROID_KEY,
      ios: Config.APP_TD_AMAP_IOS_KEY,
    }).then(() => {
      Geolocation.getCurrentPosition(
        ({ coords }) => {
          const distance = getDistance(position?.latitude, position?.longitude, coords?.latitude, coords?.longitude);

          set({
            distance: distance.toFixed(2),
          });
        },
        (err) => {
          console.error('获取定位失败', err);

          if ([2, 7].includes(err?.code)) {
            ExceptionReport({
              message: `定位失败: ${err?.message}`,
              bizName: '定位失败:(moveCameraWithLocation)',
            });
          }
        },
      );
    });
  },
  setLocationInfo: (locationInfo: Record<string, any>) => {
    set({
      locationInfo,
    });
  },
  setLocation: (location: LatLng) => {
    set({
      location,
    });
  },
  setSelectedLocation: (selectedLocation: LatLng | null) => {
    set({
      selectedLocation,
    });
  },
  setMapLocation: (location: LatLng) => {
    set({
      mapLocation: location,
    });
  },
  setCityCode: (citycode: string) => {
    set({
      citycode,
    });
  },
}));

const getDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
  const R = 6371;
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;

  return distance;
};

const deg2rad = (deg: number) => {
  return deg * (Math.PI / 180);
};

export const useGaodeNavigation = () => {
  const openGaodeMap = (location: string[]) => {
    if (location?.length !== 2) {
      TstOm.showToast({ title: '位置异常无法发起导航' });

      return;
    }

    const [longitude, latitude] = location || [];

    const gaodeAppUrl =
      Platform.OS === 'ios'
        ? `iosamap://navi?sourceApplication=塔塔运营通&lat=${latitude}&lon=${longitude}&dev=0&style=2`
        : `androidamap://navi?sourceApplication=塔塔运营通&lat=${latitude}&lon=${longitude}&dev=0&style=2`;

    Linking.canOpenURL(gaodeAppUrl)
      .then((supported) => {
        if (!supported && Platform.OS !== 'android') {
          TstOm.showToast({ title: '高德地图未安装' });
        } else {
          return Linking.openURL(gaodeAppUrl);
        }
      })
      .catch((err) => console.error('打开高德地图出错', err));
  };

  return {
    openGaodeMap,
  };
};
