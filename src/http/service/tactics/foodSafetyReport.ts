import http from '@src/http/client';
import { ClientError } from '@src/http/utils';
import {
  QueryKey,
  UndefinedInitialDataOptions,
  useMutation,
  UseMutationOptions,
  useQuery,
} from '@tanstack/react-query';

// 食安稽核报备待处理数量
export function useQueryFoodSafeReportCount({
  queryKey,
  ...options
}: { queryKey: QueryKey } & Partial<Omit<UndefinedInitialDataOptions<number, ClientError>, 'queryKey' | 'queryFn'>>) {
  return useQuery<number, ClientError>({
    queryKey,
    queryFn: async () => await http.post('/tm-api/corp/food-safety-normal-route-batch/data/apply/getWaitApproveCount'),
    ...options,
  });
}

// 报备申请
/**
 * 申请原因
 * 申请原因：SHOP_REASON-门店原因；USER_REASON-个人原因
 */
export enum FoodSafeReportApplyReason {
  门店原因 = 'SHOP_REASON',
  个人原因 = 'USER_REASON',
}

interface TFoodSafeReportApplyData {
  /**
   * 申请文件稽核
   */
  applyImages: string[] | null;
  /**
   * 申请原因
   */
  applyReason: FoodSafeReportApplyReason;
  /**
   * 申请备注
   */
  applyRemark?: null | string;
  /**
   * 申请具体原因
   */
  applySpecificReason: null | string;
  /**
   * 线路数据id
   */
  foodSafetyNormalRouteDataId: number | null;
  /**
   * 任务id
   */
  taskId: number | null;
  [property: string]: any;
}

export function useMutationFoodSafeReportApply(
  options?: Omit<UseMutationOptions<any, ClientError, TFoodSafeReportApplyData>, 'mutationFn'>,
) {
  return useMutation({
    ...options,
    mutationFn: async (data) =>
      await http.post('/tm-api/corp/food-safety-normal-route-batch/data/apply/addApply', data),
  });
}

// 稽核待办任务进度
export interface APPTodoTaskListAuditPlanProcessDTO {
  /**
   * 批次时间
   */
  batchDate?: null | string;
  /**
   * 完成数
   */
  completeCount?: number | null;
  /**
   * 总数
   */
  totalCount?: number | null;
}

export function useQueryFoodSafeTaskProcess({
  queryKey,
  ...options
}: { queryKey: QueryKey } & Partial<
  Omit<UndefinedInitialDataOptions<APPTodoTaskListAuditPlanProcessDTO[], ClientError>, 'queryKey' | 'queryFn'>
>) {
  return useQuery<APPTodoTaskListAuditPlanProcessDTO[], ClientError>({
    queryKey,
    queryFn: async () => await http.get('/tm-api/corp/app/task/todo-list/food_safety_normal_route_batch_data/process'),
    ...options,
  });
}

// 获取启用的稽核原因列表
export const getEnableReasonList = (reasonType?: 'SHOP' | 'PERSONALLY') => {
  return http.get('/tm-api/corp/audit/route/unqualified-reason/enable-list', { params: { reasonType } });
};
