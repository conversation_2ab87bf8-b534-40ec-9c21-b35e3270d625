import { FoodSafetyNormalRoutShopInfoDTO } from '@src/http/service/tactics/supervisorTask';
import Icon from '@src/ui/components/Icon';
import { Text, TouchableOpacity, View } from 'react-native';

export const ExtraShopLine = ({
  shopInfoDTOS,
  handGoToway,
  label,
  icon,
  disable,
}: {
  shopInfoDTOS?: FoodSafetyNormalRoutShopInfoDTO[];
  handGoToway: ({ location, taskId }: { location: string[]; taskId: number }) => void;
  label: string;
  icon?: string;
  disable?: boolean;
}) => {
  return shopInfoDTOS?.length ? (
    <View className="flex-row gap-x-3">
      <View className="size-6">{icon && <Icon name={icon} className="fill-white" />}</View>
      <View className="flex-1">
        <Text className="mb-4 text-display-2sm font-medium text-[#1D2129]">{label}</Text>
        <View className="flex-col gap-y-2">
          {shopInfoDTOS?.map((item) => {
            return (
              <View>
                <View className="flex-row items-center justify-between">
                  <View className="flex-row items-center gap-x-2">
                    <Text className="text-display-2sm font-medium text-[#1D2129]">{item?.shopName}</Text>
                  </View>
                  <TouchableOpacity
                    activeOpacity={1}
                    onPress={() => {
                      if (disable) {
                        return;
                      }

                      item?.longitude &&
                        item?.latitude &&
                        handGoToway({ location: [item.longitude, item.latitude], taskId: item?.taskId! });
                    }}
                  >
                    {!disable && <Icon name="gotoway" />}
                  </TouchableOpacity>
                </View>
                <Text className="mt-1 text-display-2xs text-[#86909C]">{item?.shopAddress}</Text>
                <View className="mb-[7px] mt-4 h-[1px] flex-1 bg-[#F0F0F0]" />
              </View>
            );
          })}
        </View>
      </View>
    </View>
  ) : null;
};
