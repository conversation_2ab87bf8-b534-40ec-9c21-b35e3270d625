import { useNavigation, useRoute } from '@react-navigation/native';
import { useMutationFoodSafeReportApprove } from '@src/http/service/tactics/foodSafetyReport';
import { BatchDataApplyStatus } from '@src/http/service/task-center';
import AlertDialog from '@src/ui/components/AlertDialog';
import { Button } from '@src/ui/components/Button';
import Fields from '@src/ui/template/form/Fields';
import { FormProvider } from '@src/ui/template/form/provide';
import { cn } from '@src/utils';
import { useForm } from 'react-hook-form';
import { SafeAreaView, Text, View } from 'react-native';

function Card({ children, className }: { children: React.ReactNode; className?: string }) {
  return <View className={cn('rounded-lg bg-white p-3', className)}>{children}</View>;
}

export default function FoodSafetyInspectionReject() {
  const form = useForm({ mode: 'onChange' });
  const navigation = useNavigation();
  const { params } = useRoute() as { params: { applyId: number } };

  const { mutateAsync } = useMutationFoodSafeReportApprove();

  return (
    <FormProvider value={{ form }}>
      <SafeAreaView className="flex-1">
        <View className="h-full justify-between p-3">
          <View>
            <Card>
              <Fields.Input
                label="详细说明"
                name="remark"
                inputOptions={{
                  className: 'h-[8rem] bg-[#F7F8FA] px-3',
                  maxLength: 140,
                  multiline: true,
                  textAlignVertical: 'top',
                  placeholder: '请输入详细的情况说明，最多140字',
                }}
                rules={{
                  required: {
                    value: true,
                    message: '请填写驳回说明',
                  },
                }}
              />
              <Fields.Photos
                name="approveImages"
                pickerType={['cameraPhoto', 'cropCameraVideo', 'cropPicker']}
                fileBusinessType="PATROL"
                rules={{
                  required: {
                    value: true,
                    message: '请上传附件材料',
                  },
                }}
                hiddenToastLoading={true}
                maxCount={9}
              />
            </Card>
          </View>
          <Button
            onPress={form.handleSubmit(async (values) => {
              const res = await AlertDialog.Dialog({
                title: '提示',
                description: '是否确定驳回？',
              });

              if (res === 'confirm') {
                const { approveImages, ...rest } = values || {};

                await mutateAsync({
                  ...params,
                  ...rest,
                  applyResult: BatchDataApplyStatus.审核拒绝,
                  approveImages: approveImages
                    ?.map((m: { origin: { fileId: string } }) => m?.origin?.fileId)
                    ?.filter(Boolean),
                });
                navigation.goBack();
              }
            })}
          >
            <Text className="text-base font-medium text-white">确定</Text>
          </Button>
        </View>
      </SafeAreaView>
    </FormProvider>
  );
}
