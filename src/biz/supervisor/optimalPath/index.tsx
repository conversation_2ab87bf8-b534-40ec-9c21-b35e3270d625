import { useMemo, useRef, useState } from 'react';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { useRoute } from '@react-navigation/native';
import { webviewRefPlugin } from '@src/components/WebView/WebViewPlugin';
import {
  FoodSafetyNormalRoutShopInfoDTO,
  useSupervisorFoodSafetyNormalRouteBatchDataPageList,
} from '@src/http/service/tactics/supervisorTask';
import { useGaodeNavigation } from '@src/screens/Map/hooks';
import Empty from '@src/ui/components/Empty';
import { encrypt } from '@src/utils';
import { disposeResult } from '@tastien/rn-bridge/lib/native/mange/utils';
import { Text, View } from 'react-native';
import Config from 'react-native-config';
import { MapWebView } from './components/MapWebView';

const url = `${Config.APP_WEBVIEW_URL}#/amap/routeLine`;

export default function OptimalPathIndex() {
  const { params } = useRoute() as { params: { batchId: string } };
  const [activeKey, setActiveKey] = useState<number | null>(null);
  const [Amap_Distance, setAmap_Distance] = useState<string>();
  const { openGaodeMap } = useGaodeNavigation();
  const bottomSheetRef = useRef<BottomSheet>(null);

  const { data: routeData } = useSupervisorFoodSafetyNormalRouteBatchDataPageList({
    queryKey: ['supervisorFoodSafetyNormalRouteBatchDataPageList', { params }],
    params,
    enabled: !!params?.batchId,
  });

  /**
   * 位置信息
   */
  const position = useMemo(() => {
    const canceledShopsDTOS: FoodSafetyNormalRoutShopInfoDTO[] = [];
    // 路径点
    const routePointsDTOS: FoodSafetyNormalRoutShopInfoDTO[] = [];
    // 新增门店
    const newShopPointsDTOS: FoodSafetyNormalRoutShopInfoDTO[] = [];
    // 转派门店
    const transferShopPointsDTOS: FoodSafetyNormalRoutShopInfoDTO[] = [];

    routeData?.shopInfoDTOS?.forEach((element) => {
      if (['CANCELED'].includes(element.taskStatus)) {
        canceledShopsDTOS.push(element);

        return;
      }

      if (element?.isBuffer) {
        newShopPointsDTOS.push(element);

        return;
      }

      if (element?.isTransfer) {
        transferShopPointsDTOS.push(element);

        return;
      }

      routePointsDTOS.push(element);
    });

    return {
      canceledShopsDTOS,
      routePointsDTOS,
      newShopPointsDTOS,
      transferShopPointsDTOS,
    };
  }, [routeData]);

  /**
   * 已查门店
   */
  const auditShopCount = useMemo(() => {
    return routeData?.shopInfoDTOS?.filter((item) => ['COMPLETED', 'AUDITING'].includes(item.taskStatus))?.length || 0;
  }, [routeData]);

  /**
   * 全部门店 要过滤掉作废门店
   */
  const allShopCount = useMemo(() => {
    return (routeData?.shopInfoDTOS?.length || 0) - position.canceledShopsDTOS.length;
  }, [routeData?.shopInfoDTOS?.length, position.canceledShopsDTOS.length]);

  const days = useMemo(() => {
    const minutes = routeData?.estimateExecuteTime || 0;
    const day = Math.floor(minutes / 60 / 24);
    const hours = Math.floor(minutes / 60) % 24;

    return `${day}天${hours}分钟`;
  }, [routeData?.estimateExecuteTime]);

  const activeShopInfo = useMemo(() => {
    return routeData?.shopInfoDTOS?.find((s) => {
      return s?.taskId === activeKey;
    });
  }, [activeKey, routeData?.shopInfoDTOS]);

  if (!routeData?.shopInfoDTOS?.length) {
    return <Empty text="暂无数据" />;
  }

  const handGoToway = ({ location, taskId }: { location: string[]; taskId: number }) => {
    setActiveKey(taskId);
    webviewRefPlugin.getWebViewRef?.current?.postMessage(
      JSON.stringify(disposeResult(true, { type: 'amap', location })),
    );
    openGaodeMap(location);
  };

  // 创建 ref

  return (
    <View className="flex-1 bg-[#F7F8FA]">
      <View className="flex-1">
        <MapWebView
          url={`${url}?shopInfo=${encodeURIComponent(encrypt(routeData?.shopInfoDTOS))}`}
          onCallback={(msg) => {
            setAmap_Distance(msg?.Amap_Distance);
          }}
        />
      </View>
      <BottomSheet
        ref={bottomSheetRef}
        index={0} // 初始状态为关闭，index=-1
        snapPoints={['25%', '50%']}
        onChange={() => {}}
        enablePanDownToClose={true} // 允许用户向下滑动关闭
      >
        <BottomSheetView className="flex-1 justify-center p-6">
          <Text>Awesome 🎉</Text>
        </BottomSheetView>
      </BottomSheet>
    </View>
  );
}
