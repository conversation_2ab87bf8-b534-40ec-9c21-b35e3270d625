import TstOm from '@src/components';
import { useUpload } from '@src/hooks';
import { FileBusinessType } from '@src/http/service/upload';
import Uploader, { FileVO, UploadActionParams } from '@src/ui/components/Uploader';
import { UploadProps } from '@src/ui/components/Uploader/interface';
import { cn } from '@src/utils';
import { pullAt } from 'lodash';
import { Controller } from 'react-hook-form';
import { Text, View } from 'react-native';
import Animated, { Easing, FadeIn } from 'react-native-reanimated';
import { FileProps } from '../instance';
import { useFormContext } from '../provide';

function Photos({
  label,
  pickerType,
  defaultList = [],
  customActions,
  shopId,
  latitude,
  longitude,
  maxCount,
  className,
  editable = true,
  hiddenToastLoading = false,
  getErrorMessage,
  onBeforeUpload,
  fileBusinessType,
  ...restProps
}: FileProps & {
  pickerType: UploadProps['pickerType'];
  defaultList?: UploadProps['defaultList'];
  customActions?: UploadProps['customActions'];
  onBeforeUpload?: UploadProps['onBeforeUpload'];
  shopId?: string;
  latitude?: number;
  longitude?: number;
  maxCount?: UploadProps['maxCount'];
  className?: string;
  editable?: boolean;
  /** 是否关闭 Toast 加载中（默认为false 展示 Toast 加载中） */
  hiddenToastLoading?: boolean;
  getErrorMessage?: (error: any, name: string) => string;
  fileBusinessType: FileBusinessType;
}) {
  const { form } = useFormContext();

  const { uploadFile } = useUpload(true, false, false, fileBusinessType);

  const uploadImage = async ({ file }: UploadActionParams): Promise<FileVO> => {
    const isLive = file.type === 'image/heic';
    const response = await uploadFile(
      [
        {
          ...file,
          fileName: file.name,
          contentType: isLive ? 'image/png' : file?.type,
        },
      ],
      {
        watermark: shopId ? { shopId, latitude, longitude } : undefined,
        loading: hiddenToastLoading,
      },
      // {
      //   loading: true,
      // },
    );

    TstOm.hideLoading();

    const res: any = response?.[0];

    return {
      fileId: res.id,
      filename: res.name,
      fileUrl: res.url,
      snapshotUrl: res.snapshotUrl,
      type: res?.contentType?.indexOf('video') > -1 ? 'video' : 'image',
    };
  };

  const beforeDelete: UploadProps['beforeDelete'] = (_, index, allList) => {
    pullAt(allList, [index]);

    return allList;
  };

  const onUploadAction = async (props: UploadActionParams) => {
    const res = await uploadImage(props);

    return res;
  };

  return (
    <Controller
      {...restProps}
      control={form.control}
      render={({ field: { value, onChange }, formState: { errors } }) => {
        const errorMessage = getErrorMessage?.(errors, restProps.name) || errors[restProps.name]?.message;

        return (
          <View className={cn('gap-y-2', className)}>
            <View className="relative flex-row items-center gap-x-1">
              {!!label && (
                <>
                  {(typeof restProps.rules?.required === 'boolean' && restProps.rules.required) ||
                  (typeof restProps.rules?.required === 'object' && restProps.rules?.required?.value) ? (
                    <Text className=" text-lg font-medium text-red-500">*</Text>
                  ) : null}
                  {!!label && <Text className="text-[14px] font-medium text-base-black-2">{label}</Text>}
                </>
              )}
            </View>
            {editable ? (
              <Uploader
                compress
                preview
                uploadAction={onUploadAction}
                pickerType={pickerType}
                onBeforeUpload={onBeforeUpload}
                defaultList={defaultList}
                beforeDelete={beforeDelete}
                onChange={onChange}
                list={value}
                customActions={customActions}
                maxCount={maxCount}
              />
            ) : (
              <Uploader.Preview list={value?.map(({ origin }: any) => ({ ...origin }))} />
            )}

            {typeof errorMessage === 'string' ? (
              <Animated.Text entering={FadeIn.duration(500).easing(Easing.ease)} className="text-[14px] text-red-500">
                {(errorMessage as string) ?? ''}
              </Animated.Text>
            ) : null}
          </View>
        );
      }}
    />
  );
}

export default Photos;
