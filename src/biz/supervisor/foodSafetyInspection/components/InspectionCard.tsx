import { useMemo } from 'react';
import { useNavigation } from '@react-navigation/native';
import {
  FoodSafeReportApplyReasonCN,
  FoodSafetyNormalRouteBatchDataApplyListResp,
  useMutationFoodSafeReportApprove,
} from '@src/http/service/tactics/foodSafetyReport';
import { BatchDataApplyStatus, BatchDataApplyStatusCN } from '@src/http/service/task-center';
import Toast from '@src/ui/components/Toast';
import dayjs from 'dayjs';
import { Text, View } from 'react-native';
import show from './PassPopup';
import { StatusBtn } from './StatusBtn';
import { InfoCard } from '../../foodSafetyComplaint/components/ComplaintDescCard';
import { ISeparator } from '../../foodSafetyComplaint/components/ISeparator';

export const InspectionCard = ({
  status,
  approveExpiredTime,
  shopId,
  shopName,
  applyReason,
  applySpecificReason,
  applyRemark,
  applyImages,
  id,
  canApprove,
  isDetail,
  applyType,
  batchDataBatchId,
  onRefresh,
  applyUserId,
}: FoodSafetyNormalRouteBatchDataApplyListResp & {
  isDetail?: boolean;
  onRefresh?: () => void;
}) => {
  const navigation = useNavigation<any>();

  // 是否是待审核
  const isPending = useMemo(() => {
    return status === BatchDataApplyStatus.待审核;
  }, [status]);

  const { mutateAsync } = useMutationFoodSafeReportApprove();

  return (
    <>
      <View className="rounded-lg bg-white p-3">
        <View className="flex-col gap-y-1">
          <View className="flex-row items-center justify-between">
            <Text className="text-display-2sm text-[#1D2129]">
              {shopId} {shopName}
            </Text>
            <Text className="text-display-sm font-medium  text-[#0E42D2]">{BatchDataApplyStatusCN[status!]}</Text>
          </View>
          {!!approveExpiredTime && (
            <Text className="text-display-2xs font-medium  text-[#86909C]">
              审核截止至{dayjs(approveExpiredTime).format('YYYY/MM/DD HH:mm')}
            </Text>
          )}
        </View>
        <ISeparator />
        <View>
          <View className="flex-row items-center">
            <Text className="text-display-sm font-medium  text-[#1D2129]">申请异常原因：</Text>
            <Text className="rounded-[2px] border-[0.5px] border-[#7BE188] px-1 text-display-2xs text-[#7BE188]">
              {FoodSafeReportApplyReasonCN[applyReason!]}
            </Text>
          </View>
          <Text className="text-display-sm font-medium  text-[#1D2129]">具体原因：{applySpecificReason}</Text>
          <InfoCard remarkLabel="辅助材料" remark={applyRemark} attachments={applyImages} containerClassName="mt-2" />
        </View>
        <ISeparator />
        {/* CTODO:任务进度 */}
        {/* 操作按钮区域 */}
        {
          <>
            <View className=" flex-row gap-x-2">
              {isPending && !!canApprove && !isDetail ? (
                <>
                  <StatusBtn
                    status={BatchDataApplyStatus.审核拒绝}
                    label="驳回"
                    currentStatus={status}
                    onPress={() => {
                      navigation.navigate('FoodSafetyInspectionReject', {
                        applyId: id,
                      });
                    }}
                  />
                  <StatusBtn
                    status={BatchDataApplyStatus.审核通过}
                    label="审核通过"
                    currentStatus={status}
                    onPress={async () => {
                      try {
                        const res = await show({
                          type: applyType!,
                          minDistanceBufferShoplPayload: {
                            shopId,
                            batchId: batchDataBatchId,
                          },
                          applyUserId,
                        });

                        console.log('💀 ~ res:', res);

                        if (!res) {
                          return;
                        }

                        await mutateAsync({
                          ...res,
                          applyResult: BatchDataApplyStatus.审核通过,
                          applyId: id,
                        });

                        Toast.success('审核成功');
                        onRefresh?.();
                      } catch (error) {
                        Toast.fail('审核失败');
                      }
                    }}
                  />
                </>
              ) : (
                <>
                  {[BatchDataApplyStatus.审核通过, BatchDataApplyStatus.审核拒绝].includes(status!) && (
                    <StatusBtn status={status!} currentStatus={status} disabled />
                  )}
                </>
              )}
            </View>
          </>
        }
      </View>
    </>
  );
};
