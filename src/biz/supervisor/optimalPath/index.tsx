import { useMemo, useState } from 'react';
import { useRoute } from '@react-navigation/native';
import { webviewRefPlugin } from '@src/components/WebView/WebViewPlugin';
import {
  FoodSafetyNormalRoutShopInfoDTO,
  useSupervisorFoodSafetyNormalRouteBatchDataPageList,
} from '@src/http/service/tactics/supervisorTask';
import Empty from '@src/ui/components/Empty';
import { encrypt } from '@src/utils';
import { disposeResult } from '@tastien/rn-bridge/lib/native/mange/utils';
import dayjs from 'dayjs';
import { isNil } from 'lodash';
import { ScrollView, Text, View } from 'react-native';
import Config from 'react-native-config';
import { ExtraShopLine } from './components/ExtraShopLine';
import { MapInfo } from './components/MapInfo';
import { MapWebView } from './components/MapWebView';
import { TimeLine } from './components/TimeLine';

const url = `${Config.APP_WEBVIEW_URL}#/amap/routeLine`;

export default function OptimalPathIndex() {
  const { params } = useRoute() as { params: { batchId: string } };
  const [activeKey, setActiveKey] = useState<number | null>(null);
  const [Amap_Distance, setAmap_Distance] = useState<string>();

  const { data: routeData } = useSupervisorFoodSafetyNormalRouteBatchDataPageList({
    queryKey: ['supervisorFoodSafetyNormalRouteBatchDataPageList', { params }],
    params,
    enabled: !!params?.batchId,
  });

  /**
   * 位置信息
   */
  const position = useMemo(() => {
    const canceledShopsDTOS: FoodSafetyNormalRoutShopInfoDTO[] = [];
    // 路径点
    const routePointsDTOS: FoodSafetyNormalRoutShopInfoDTO[] = [];
    // 新增门店
    const newShopPointsDTOS: FoodSafetyNormalRoutShopInfoDTO[] = [];
    // 转派门店
    const transferShopPointsDTOS: FoodSafetyNormalRoutShopInfoDTO[] = [];

    routeData?.shopInfoDTOS?.forEach((element) => {
      if (['CANCELED'].includes(element.taskStatus)) {
        canceledShopsDTOS.push(element);

        return;
      }

      if (element?.isBuffer) {
        newShopPointsDTOS.push(element);

        return;
      }

      if (element?.isTransfer) {
        transferShopPointsDTOS.push(element);

        return;
      }

      routePointsDTOS.push(element);
    });

    return {
      canceledShopsDTOS,
      routePointsDTOS,
      newShopPointsDTOS,
      transferShopPointsDTOS,
    };
  }, [routeData]);

  /**
   * 已查门店
   */
  const auditShopCount = useMemo(() => {
    return routeData?.shopInfoDTOS?.filter((item) => ['COMPLETED', 'AUDITING'].includes(item.taskStatus))?.length || 0;
  }, [routeData]);

  /**
   * 全部门店 要过滤掉作废门店
   */
  const allShopCount = useMemo(() => {
    return (routeData?.shopInfoDTOS?.length || 0) - position.canceledShopsDTOS.length;
  }, [routeData?.shopInfoDTOS?.length, position.canceledShopsDTOS.length]);

  const days = useMemo(() => {
    const minutes = routeData?.estimateExecuteTime || 0;
    const day = Math.floor(minutes / 60 / 24);
    const hours = Math.floor(minutes / 60) % 24;

    return `${day}天${hours}分钟`;
  }, [routeData?.estimateExecuteTime]);

  const activeShopInfo = useMemo(() => {
    return routeData?.shopInfoDTOS?.find((s) => {
      return s?.taskId === activeKey;
    });
  }, [activeKey, routeData?.shopInfoDTOS]);

  if (!routeData?.shopInfoDTOS?.length) {
    return <Empty text="暂无数据" />;
  }

  const handGoToway = ({ location, taskId }: { location: string[]; taskId: number }) => {
    setActiveKey(taskId);
    webviewRefPlugin.getWebViewRef?.current?.postMessage(
      JSON.stringify(disposeResult(true, { type: 'amap', location })),
    );
  };

  return (
    <View className="flex-1 bg-[#F7F8FA]">
      <View className="bg-white">
        <View className="h-90">
          <MapWebView
            url={`${url}?shopInfo=${encodeURIComponent(encrypt(routeData?.shopInfoDTOS))}`}
            onCallback={(msg) => {
              setAmap_Distance(msg?.Amap_Distance);
            }}
          />
        </View>
        <MapInfo activeShopInfo={!isNil(activeKey) ? activeShopInfo : undefined} />
      </View>
      <ScrollView className="mt-3 flex flex-1 bg-white">
        <View className="p-3">
          <View className="flex-col gap-y-1 rounded-lg bg-[#F7F8FA] p-3">
            <View className="flex-row items-center justify-between">
              <View className="flex-row gap-x-1">
                <View className="rounded border-[0.5px] border-[#0E42D2] px-[4.5px]">
                  <Text className="text-display-xs text-[#0E42D2]">{routeData?.batchId}</Text>
                </View>
                <Text className="text-display-2sm font-medium text-[#1D2129]">
                  已查门店：
                  {auditShopCount}/{allShopCount}
                </Text>
              </View>
            </View>
            <View>
              <View className="flex-row items-center gap-x-2">
                <Text className="text-display-sm text-[#4E5969]">
                  途经点： {position?.routePointsDTOS?.length - 2}个门店
                </Text>
                <View className="h-4 w-[0.5px] bg-[#C9CDD4]" />
                <Text className="text-display-sm text-[#4E5969]">总行程：{Amap_Distance || '-'}公里</Text>
              </View>
              <Text className="text-display-sm text-[#4E5969]">
                执行时段：{dayjs(routeData?.beginTime).format('YYYY/MM/DD HH:mm')} -{' '}
                {dayjs(routeData?.endTime).format('YYYY/MM/DD HH:mm')}
              </Text>
              <Text className="text-display-sm text-[#4E5969]">预估共{days}</Text>
            </View>
          </View>
          <View className="mt-4">
            <TimeLine shopInfoDTOS={position?.routePointsDTOS} handGoToway={handGoToway} />
            <ExtraShopLine
              shopInfoDTOS={position?.newShopPointsDTOS}
              handGoToway={handGoToway}
              icon="add_icon"
              label="额外增加门店"
            />
            <ExtraShopLine
              shopInfoDTOS={position?.transferShopPointsDTOS}
              handGoToway={handGoToway}
              icon="change_person"
              label="转派门店"
            />
            <ExtraShopLine
              shopInfoDTOS={position?.canceledShopsDTOS}
              handGoToway={handGoToway}
              label="已作废的门店"
              disable
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
}
