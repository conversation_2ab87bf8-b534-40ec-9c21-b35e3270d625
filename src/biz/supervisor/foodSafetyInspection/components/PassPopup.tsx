import { memo, useEffect, useMemo, useState } from 'react';
import { useUserInfoList } from '@src/http/service/diagnostic';
import {
  applyTypeEnum,
  useQueryFoodSafeMinDistanceBufferShop,
  useQueryFoodSafeRouteIds,
} from '@src/http/service/tactics/foodSafetyReport';
import { ShopStatusCN } from '@src/http/service/task-center';
import { Loading } from '@src/ui/components/components';
import Icon from '@src/ui/components/Icon';
import Popup from '@src/ui/components/Popup';
import Portal from '@src/ui/components/Portal';
import Fields from '@src/ui/template/form/Fields';
import { FormProvider } from '@src/ui/template/form/provide';
import { cn } from '@src/utils';
import { debounce, isNil } from 'lodash';
import { useForm } from 'react-hook-form';
import { Text, TouchableOpacity, View } from 'react-native';
import { ISeparator } from '../../foodSafetyComplaint/components/ISeparator';

interface PassPopupProps {
  onClose?: () => void;
  onSubmit?: (val: any) => void;
  type: applyTypeEnum;
  minDistanceBufferShoplPayload?: { shopId?: string; batchId?: string };
  applyUserId?: number;
}

const CustomDescRender = ({ label, value }: { label: string; value: any }) => {
  return (
    <View className="h-11 flex-row items-center justify-between rounded-lg bg-[#F7F8FA] px-3">
      <Text className={cn('text-display-2sm', !isNil(value) ? 'text-[#1D2129]' : 'text-[#86909C]')}>{label}</Text>
      <Icon name="chevron-right" />
    </View>
  );
};

export const PassPopup = memo(
  ({ onClose, onSubmit, type, minDistanceBufferShoplPayload, applyUserId }: PassPopupProps) => {
    const [visible, setVisible] = useState(false);
    const form = useForm({ mode: 'onChange' });

    const { data: userList } = useUserInfoList({ staleTime: 1000 * 30 });

    const userOptions = useMemo(() => {
      const _options =
        userList?.map((v) => ({ label: v.nickName, value: v.userId }))?.filter((v) => v.value !== applyUserId) || [];

      return _options;
    }, [applyUserId, userList]);

    const { data, isLoading } = useQueryFoodSafeMinDistanceBufferShop({
      queryKey: ['foodSafeMinDistanceBufferShop', minDistanceBufferShoplPayload],
      params: minDistanceBufferShoplPayload as any,
      enabled: !!minDistanceBufferShoplPayload && type === applyTypeEnum.MODIFY_SHOP,
    });

    const [routeOption, setRouteOption] = useState<{ label: string; value: string }[]>([]);
    const newProcessUserId = form.watch('newProcessUserId');
    const { data: routeIds, isLoading: isRouteIdsLoading } = useQueryFoodSafeRouteIds({
      queryKey: ['foodSafeRouteIds', newProcessUserId],
      params: { processUserId: newProcessUserId },
      enabled: !!newProcessUserId,
    });

    useEffect(() => {
      form.setValue('newBatchId', undefined);
    }, [form, newProcessUserId]);

    useEffect(() => {
      if (newProcessUserId) {
        setRouteOption(() => {
          return (
            routeIds?.map(({ id, batchId }: any) => ({
              label: batchId,
              value: id,
            })) || []
          );
        });
      }
    }, [newProcessUserId, routeIds]);

    function closeModal() {
      setVisible(false);
      // 先关闭后，在销毁
      setTimeout(() => {
        onClose?.();
      }, 500);
    }

    useEffect(() => {
      setVisible(true);
    }, []);

    function handleRequestPopupClose() {
      closeModal();

      return true;
    }

    const onFinish = debounce(() => {
      form.handleSubmit((values) => {
        setVisible(false);

        setTimeout(() => {
          onSubmit?.(values);
        }, 300);
      })();
    }, 500);

    const buildModalInfo = () => {
      if (type === applyTypeEnum.MODIFY_USER) {
        return (
          <FormProvider value={{ form }}>
            <View className="flex-col gap-y-4">
              <Fields.Selector
                label="转办人员"
                name="newProcessUserId"
                selectorOption={{
                  title: '转办人员',
                  options: userOptions,
                  search: true,
                }}
                componentClassName="flex-col gap-y-2"
                placeholder="请选择转办人员"
                cellContainerClassName="py-0"
                customDescRender={CustomDescRender}
                rules={{
                  required: {
                    value: true,
                    message: '请选择转办人员',
                  },
                }}
              />
              <Fields.Selector
                label="将任务迁移至以下路径"
                name="newBatchId"
                selectorOption={{
                  title: '路径编号',
                  options: routeOption,
                  search: true,
                }}
                componentClassName="flex-col gap-y-2"
                placeholder="请选择路径编号"
                cellContainerClassName="py-0"
                customDescRender={CustomDescRender}
                loading={isRouteIdsLoading}
              />
            </View>
          </FormProvider>
        );
      }

      if (type === applyTypeEnum.MODIFY_SHOP && !!data?.shopId) {
        return (
          <View className="flex-col gap-y-4">
            <Text className="text-display-base text-[#4E5969]">
              审核通过后将自动作废此门店稽核任务，并自动新分配以下门店稽核任务：
            </Text>
            <ISeparator />
            <View className="flex-col gap-y-1">
              <View className="flex-row  items-center">
                <Text className="text-display-2sm font-medium text-[#1D2129]">
                  {data?.shopId} {data?.shopName}
                </Text>
                <Text className="text-display-2xs text-[#86909C]">
                  （{ShopStatusCN[data?.shopStatus as keyof typeof ShopStatusCN]}）
                </Text>
              </View>
              <View className="flex-row gap-x-1">
                <Icon name="location-v2" className="mt-0.5 size-4 text-[#86909C]" />
                <Text className="flex-1 text-display-2xs text-[#86909C]">{data?.shopAddress}</Text>
              </View>
            </View>
          </View>
        );
      }

      if (type === applyTypeEnum.MODIFY_SHOP && !data?.shopId) {
        return (
          <View className="flex-col gap-y-4">
            <Text className="text-display-base text-[#4E5969]">
              审核通过后将自动作废此门店稽核任务，此路线隐藏门店已经全部激活，因此不会自动分配新的任务。
            </Text>
          </View>
        );
      }
    };

    return (
      <Popup
        round
        destroyOnClosed
        visible={visible}
        onPressOverlay={closeModal}
        onRequestClose={handleRequestPopupClose}
      >
        <View className="w-full rounded-xl bg-white">
          <View className="px-6 py-8">
            <View className="mb-2 w-full flex-row justify-center">
              <Text className="text-display-base font-medium text-[#1D2129]">审核通过确认</Text>
            </View>
            {isLoading ? (
              <View className="flex-1 items-center justify-center">
                <Loading />
              </View>
            ) : (
              buildModalInfo()
            )}
          </View>
          <View className="h-14 flex-row border-t-[0.5px] border-[#E5E6EB]">
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={closeModal}
              className="flex-1 items-center justify-center border-r-[0.5px] border-[#E5E6EB]"
            >
              <Text className="text-display-base font-medium text-[#1D2129]">取消</Text>
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.8}
              className="flex-1 items-center justify-center"
              onPress={() => {
                if (type === applyTypeEnum.MODIFY_USER) {
                  return onFinish();
                }

                if (type === applyTypeEnum.MODIFY_SHOP && !!data?.shopId) {
                  return onSubmit?.({ newShopId: data?.shopId });
                }

                if (type === applyTypeEnum.MODIFY_SHOP && !data?.shopId) {
                  return onSubmit?.({});
                }
              }}
            >
              <Text className="text-display-base font-medium text-[#0052D9]">确定</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Popup>
    );
  },
);

export default function show({
  type,
  minDistanceBufferShoplPayload,
  applyUserId,
}: {
  type: applyTypeEnum;
  minDistanceBufferShoplPayload?: { shopId?: string; batchId?: string };
  applyUserId?: number;
}) {
  return new Promise<any | null>((resolve) => {
    const key = Portal.add(
      <PassPopup
        onClose={() => {
          Portal.remove(key);
          resolve(null);
        }}
        onSubmit={(val) => {
          Portal.remove(key);
          resolve(val);
        }}
        type={type}
        minDistanceBufferShoplPayload={minDistanceBufferShoplPayload}
        applyUserId={applyUserId}
      />,
    );
  });
}
