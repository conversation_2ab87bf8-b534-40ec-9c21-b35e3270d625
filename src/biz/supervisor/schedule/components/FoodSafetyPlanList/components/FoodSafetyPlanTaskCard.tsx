import { useMemo } from 'react';
import { useNavigation } from '@react-navigation/native';
import { faceModule } from '@src/components/WebView/WebViewPlugin';
import { Items, TaskStatus, TaskTransferStatus } from '@src/http/service/task-center';
import { ShopStatusEnum } from '@src/http/service/taskCenter';
import { StrategyTaskStatusCN } from '@src/screens/StrategyTaskComponents/constants';
import { StrategyTaskStatus } from '@src/screens/TaskDetailStrategy/constants/strategy';
import useConfig from '@src/store/useConfig';
import { Button, Icon, Text as RNText } from '@src/ui/components/components';
import { ESign } from '@src/ui/template/task/Card/inspection';
import { encrypt, SignTypeText } from '@src/utils';
import dayjs from 'dayjs';
import { Text, View } from 'react-native';

enum EBtn {
  签到,
  签离,
  签到并巡店,
  巡店,
  报备,
}

export const FoodSafetyPlanTaskCard = (
  data: Items & {
    startTime?: string;
    expiredTime?: string;
    allowExpiredTime?: string;
  },
) => {
  const { hasLeaved, applicationStatus, signType, taskStatus, hasSigned } = data || {};
  const navigation = useNavigation<TstOm.StackParams.ScreenNavigationProp['navigation']>();
  const { config } = useConfig();
  /** 系统的签到配置 */
  const systemCheckInConfig = +config?.MUST_CHECK_IN as ESign;

  // 未开始 禁用
  const notStartedDisabled = false;

  // 判断任务是否需要签到
  const isNeedSign = useMemo(() => {
    if (applicationStatus === TaskTransferStatus.转办待审核) {
      return false;
    }

    const isSignType = [SignTypeText.签到, SignTypeText.签到签离].includes(signType as SignTypeText);
    const isFollowSystem = signType === SignTypeText.跟随系统 && [1, 2].includes(systemCheckInConfig);

    return (isSignType || isFollowSystem) && taskStatus === 'WAITING_START';
  }, [applicationStatus, signType, taskStatus, systemCheckInConfig]);

  // 计算权限按钮
  const btnList = useMemo(() => {
    if (isNeedSign) {
      return [EBtn.签到];
    }

    // 根据任务状态和签到类型的组合来返回按钮
    const getButtonsForStatus = () => {
      switch (taskStatus) {
        case TaskStatus.待开始: {
          const _btn = [EBtn.报备];

          switch (signType) {
            case SignTypeText.跟随系统:
              if (systemCheckInConfig === ESign.不需要签到) {
                return [..._btn, EBtn.签到, EBtn.巡店];
              } else if (systemCheckInConfig === ESign.需要签到 || systemCheckInConfig === ESign.需要签到签离) {
                return [..._btn, EBtn.签到并巡店];
              }

              break;

            case SignTypeText.不需要签到:
              return [..._btn, EBtn.签到, EBtn.巡店];

            case SignTypeText.签到:
            case SignTypeText.签到签离:
              return [..._btn, EBtn.签到并巡店];
          }

          break;
        }

        case TaskStatus.逾期进行中:
        case TaskStatus.进行中: {
          if (signType === SignTypeText.跟随系统) {
            if (systemCheckInConfig === ESign.不需要签到) {
              if (!hasSigned) {
                return [EBtn.签到, EBtn.巡店];
              } else if (hasSigned) {
                return [EBtn.签离, EBtn.巡店];
              }
            } else if (systemCheckInConfig === ESign.需要签到 || systemCheckInConfig === ESign.需要签到签离) {
              return [EBtn.巡店];
            }
          }

          if (signType === SignTypeText.不需要签到) {
            if (!hasSigned) {
              return [EBtn.签到, EBtn.巡店];
            } else if (hasSigned) {
              return [EBtn.签离, EBtn.巡店];
            }
          }

          if (signType === SignTypeText.签到 || signType === SignTypeText.签到签离) {
            return [EBtn.巡店];
          }

          if (signType === SignTypeText.不需要签到 && hasLeaved) {
            return [EBtn.巡店];
          }

          break;
        }

        default:
          return [];
      }
    };

    return getButtonsForStatus();
  }, [hasLeaved, hasSigned, signType, taskStatus, isNeedSign, systemCheckInConfig]);

  // 按钮映射
  const BtnMap: Record<EBtn, JSX.Element> = {
    [EBtn.签到]: (
      <Button
        key={EBtn.签到}
        onPress={() => {
          faceModule.navigateTo({
            param: encrypt({
              shopId: data?.shopId,
              taskId: data?.id,
              isNew: true,
              selectionCompleted: false,
              selectorNumber: data?.mustCount,
              path: 'SupervisorSignIn',
              callNativeBackUrl: 'SupervisorStrategyTask',
              isGrayUser: true,
            }),
            native: true,
          });
        }}
        className="h-8 min-w-18 rounded-lg border border-[#C9CDD4]"
        variant="outline"
        size="sm"
        disabled={notStartedDisabled}
      >
        <RNText className="text-display-sm text-[#1D2129]">签到</RNText>
      </Button>
    ),
    [EBtn.签离]: (
      <Button
        key={EBtn.签离}
        onPress={() => {
          faceModule.navigateTo({
            param: encrypt({
              shopId: data.shopId,
              taskId: data?.id,
              isNew: true,
              path: 'SupervisorCheckout',
              isGrayUser: true,
            }),
            native: true,
          });
        }}
        className="h-8 min-w-18 rounded-lg border border-[#C9CDD4]"
        variant="outline"
        size="sm"
      >
        <RNText className="text-display-sm text-[#1D2129]">签离</RNText>
      </Button>
    ),
    [EBtn.签到并巡店]: (
      <Button
        key={EBtn.签到并巡店}
        onPress={() => {
          faceModule.navigateTo({
            param: encrypt({
              shopId: data.shopId,
              taskId: data.id,
              path: 'SupervisorSignIn',
              selectionCompleted: false,
              selectorNumber: data?.mustCount,
              isGrayUser: true,
              isNew: true,
              callNativeBackUrl: 'SupervisorStrategyTask',
            }),
          });
        }}
        className="h-8 min-w-18 rounded-lg border border-[#C9CDD4]"
        variant="outline"
        size="sm"
        disabled={notStartedDisabled}
      >
        <RNText className="text-display-sm text-[#1D2129]">签到并执行</RNText>
      </Button>
    ),
    [EBtn.巡店]: (
      <Button
        key={EBtn.巡店}
        size="sm"
        onPress={() => {
          if (data?.mustCount) {
            navigation.navigate(
              'SupervisorOptions',
              encrypt({
                taskId: data?.id,
                selectionCompleted: false,
                hasReview: data?.hasNeedReview,
                selectorNumber: data?.mustCount,
                isGrayUser: true,
                callNativeBackUrl: 'SupervisorStrategyTask',
              }),
            );

            return;
          }

          navigation.navigate('SupervisorStrategyTask', {
            taskId: data?.id,
          });
        }}
        className="h-8 min-w-18 rounded-lg border border-[#C9CDD4]"
        variant="outline"
        disabled={notStartedDisabled}
      >
        <RNText className="text-display-sm text-[#1D2129]">执行</RNText>
      </Button>
    ),
    [EBtn.报备]: (
      <Button
        key={EBtn.报备}
        onPress={() => {
          navigation.navigate('ReportApplicationIndex', {
            foodSafetyNormalRouteDataId: data?.foodSafetyNormalRouteDataId,
            taskId: data?.id,
          });
        }}
        className="h-8 min-w-18 rounded-lg border border-[#C9CDD4]"
        variant="outline"
        size="sm"
        disabled={notStartedDisabled}
      >
        <RNText className="text-display-sm text-[#1D2129]">报备</RNText>
      </Button>
    ),
  };

  return (
    <View className="border-t-[0.5px] border-[#E5E6EB] bg-white p-3">
      <View className="flex-row items-center">
        <Text className="text-display-2sm font-medium text-[#1D2129]">
          {data?.shopId} {data?.shopName}
        </Text>
        <Text className="text-disaplay-2xs ml-3 text-[#86909C]">{`(${ShopStatusEnum?.[data?.shopStatus as any]})`}</Text>
      </View>
      <View className="mt-1">
        <View className="flex-row gap-x-1">
          <Icon name="location-v2" className="mt-0.5 size-4 text-[#86909C]" />
          <Text className="flex-1 text-display-2xs text-[#86909C]">{data?.shopAddress}</Text>
        </View>
        <Text className="text-display-2xs text-[#86909C]">
          执行截止时间：{dayjs(data?.expiredTime).format('YYYY/MM/DD HH:mm:ss')}
        </Text>
        {data?.taskStatus === StrategyTaskStatus.EXPIRED_RUNNING && (
          <Text className="text-display-2xs text-[#F1403A]">
            逾期可执行时间：{dayjs(data?.allowExpiredTime).format('YYYY/MM/DD HH:mm:ss')}
          </Text>
        )}
      </View>
      <View className="mt-2 flex-row items-center justify-between rounded-[6px] bg-[#F7F8FA] px-3 py-2">
        <Text className="text-display-sm text-[#4E5969]">{data?.taskName}</Text>
        <Text className="text-display-sm text-[#0E42D2]">
          {StrategyTaskStatusCN[data?.taskStatus as keyof typeof StrategyTaskStatusCN]}
        </Text>
      </View>
      {!!btnList?.length && (
        <View className="mt-3 flex-row items-center justify-end gap-x-2">
          {btnList?.map((item) => BtnMap[item as keyof typeof BtnMap])}
        </View>
      )}
    </View>
  );
};
